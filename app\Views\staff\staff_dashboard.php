<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Dashboard Header -->
<div class="row mb-4">
    <div class="col-md-6">
        <h2><i class="fas fa-tachometer-alt me-2"></i>Staff Dashboard</h2>
        <p class="text-muted">Welcome to the AgriStats Management System</p>
    </div>
    <div class="col-md-6 text-end">
        <div class="btn-group">
            <a href="<?= base_url('exercises/create') ?>" class="btn btn-success">
                <i class="fas fa-plus-circle me-2"></i>New Exercise
            </a>
            <a href="<?= base_url('staff/farmers') ?>" class="btn btn-primary">
                <i class="fas fa-user-plus me-2"></i>Add Farmer
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <!-- Farmers Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Farmers</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $farmer_count ?? 0 ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="<?= base_url('staff/farmers') ?>" class="text-primary small">View Details <i class="fas fa-arrow-right"></i></a>
            </div>
        </div>
    </div>

    <!-- Crop Farm Blocks Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Crop Farm Blocks</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $crop_blocks_count ?? 0 ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-seedling fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="<?= base_url('staff/farms') ?>" class="text-success small">View Details <i class="fas fa-arrow-right"></i></a>
            </div>
        </div>
    </div>

    <!-- Livestock Farm Blocks Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Livestock Farm Blocks</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $livestock_blocks_count ?? 0 ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-horse fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="<?= base_url('staff/livestock/farm-blocks') ?>" class="text-info small">View Details <i class="fas fa-arrow-right"></i></a>
            </div>
        </div>
    </div>

    <!-- Crop Buyers Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Crop Buyers</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $crop_buyers_count ?? 0 ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-handshake fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="<?= base_url('staff/buyers') ?>" class="text-warning small">View Details <i class="fas fa-arrow-right"></i></a>
            </div>
        </div>
    </div>
</div>

<!-- Active Exercise and Recent Data Summary -->
<div class="row mb-4">
    <!-- Active Exercise -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0"><i class="fas fa-clipboard-check me-2"></i>Active Exercises</h5>
                <a href="<?= base_url('exercises') ?>" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if (!empty($active_exercises)): ?>
                <div class="table-responsive">
                    <table class="table table-hover table-sm">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>End Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($active_exercises as $exercise): ?>
                            <tr>
                                <td>
                                    <a href="<?= base_url('exercises/view/' . $exercise['id']) ?>">
                                        <?= esc($exercise['title']) ?>
                                    </a>
                                </td>
                                <td><?= date('M d, Y', strtotime($exercise['date_to'])) ?></td>
                                <td><span class="badge bg-success">Active</span></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <div class="mb-3">
                        <i class="fas fa-tasks fa-3x text-muted"></i>
                    </div>
                    <p class="text-muted mb-0">No active exercises found.</p>
                    <a href="<?= base_url('exercises/create') ?>" class="btn btn-sm btn-outline-primary mt-2">Create New Exercise</a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Recent Data Summary -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0"><i class="fas fa-chart-pie me-2"></i>Data Distribution</h5>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="reportsDropdown" data-bs-toggle="dropdown">
                        Reports
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="reportsDropdown">
                        <li><a class="dropdown-item" href="<?= base_url('staff/reports/farmers') ?>">Farmers Report</a></li>
                        <li><a class="dropdown-item" href="<?= base_url('staff/reports/crops') ?>">Crops Report</a></li>
                        <li><a class="dropdown-item" href="<?= base_url('staff/reports/blocks') ?>">Blocks Report</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <canvas id="distributionChart" width="400" height="250"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity and Quick Links -->
<div class="row">
    <!-- Recent Activity -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0"><i class="fas fa-list me-2"></i>Exercises Overview</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="exercisesTable">
                <thead>
                    <tr>
                        <th>Title</th>
                        <th>Date Range</th>
                        <th>Responsible</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($exercises)) : ?>
                        <?php foreach ($exercises as $exercise) : ?>
                            <tr>
                                <td><?= esc($exercise['title']) ?></td>
                                <td><?= date('M d, Y', strtotime($exercise['date_from'])) ?> - <?= date('M d, Y', strtotime($exercise['date_to'])) ?></td>
                                <td><?= esc($exercise['officer_name'] ?? 'N/A') ?></td>
                                <td>
                                    <?php
                                    $statusBadge = 'secondary';
                                    switch ($exercise['status']) {
                                        case 'active':
                                            $statusBadge = 'success';
                                            break;
                                        case 'draft':
                                            $statusBadge = 'warning';
                                            break;
                                        case 'cancelled':
                                            $statusBadge = 'danger';
                                            break;
                                        case 'submitted':
                                            $statusBadge = 'info';
                                            break;
                                        case 'approved':
                                            $statusBadge = 'primary';
                                            break;
                                    }
                                    ?>
                                    <span class="badge bg-<?= $statusBadge ?>"><?= ucfirst(esc($exercise['status'])) ?></span>
                                </td>
                                <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="<?= base_url('exercises/view/' . $exercise['id']) ?>" class="btn btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                                <a href="<?= base_url('exercises/edit/' . $exercise['id']) ?>" class="btn btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                            </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else : ?>
                        <tr>
                            <td colspan="5" class="text-center">No exercises found. <a href="<?= base_url('exercises/create') ?>">Create your first exercise</a>.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Links and Activity Feed -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0"><i class="fas fa-link me-2"></i>Quick Links</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 mb-3">
                        <a href="<?= base_url('staff/farms/maps') ?>" class="btn btn-light btn-block d-flex align-items-center justify-content-center p-3 h-100 w-100">
                            <div class="text-center">
                                <i class="fas fa-map-marked-alt fa-2x mb-2 text-success"></i>
                                <div>Field Maps</div>
                            </div>
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="<?= base_url('staff/farms/crops-blocks') ?>" class="btn btn-light btn-block d-flex align-items-center justify-content-center p-3 h-100 w-100">
                            <div class="text-center">
                                <i class="fas fa-seedling fa-2x mb-2 text-primary"></i>
                                <div>Crops Data</div>
                            </div>
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="<?= base_url('staff/farms/harvest_data') ?>" class="btn btn-light btn-block d-flex align-items-center justify-content-center p-3 h-100 w-100">
                            <div class="text-center">
                                <i class="fas fa-apple-alt fa-2x mb-2 text-danger"></i>
                                <div>Harvests</div>
                            </div>
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="<?= base_url('staff/reports') ?>" class="btn btn-light btn-block d-flex align-items-center justify-content-center p-3 h-100 w-100">
                            <div class="text-center">
                                <i class="fas fa-chart-bar fa-2x mb-2 text-info"></i>
                                <div>Reports</div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card shadow">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0"><i class="fas fa-calendar-alt me-2"></i>Calendar</h5>
            </div>
            <div class="card-body p-0">
                <div id="mini-calendar" class="p-3">
                    <!-- Calendar will be rendered by JavaScript -->
                    <div class="text-center py-4">
                        <div class="mb-2">
                            <i class="far fa-calendar-alt fa-3x text-muted"></i>
                        </div>
                        <p class="text-muted mb-0">Calendar view coming soon.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Initialize DataTable for exercises
    if ($('#exercisesTable tbody tr').length > 0 && !$('#exercisesTable tbody tr:first').find('td[colspan]').length) {
        $('#exercisesTable').DataTable({
            responsive: true,
            pageLength: 5,
            lengthMenu: [[5, 10, 25, -1], [5, 10, 25, "All"]],
            order: [[3, 'asc']]
        });
    }

    // Initialize Chart.js for data distribution
    var ctx = document.getElementById('distributionChart').getContext('2d');
    var distributionChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Farmers', 'Crop Blocks', 'Livestock Blocks', 'Active Exercises'],
            datasets: [{
                data: [
                    <?= $farmer_count ?? 0 ?>, 
                    <?= $crop_blocks_count ?? 0 ?>, 
                    <?= $livestock_blocks_count ?? 0 ?>, 
                    <?= !empty($active_exercises) ? count($active_exercises) : 0 ?>
                ],
                backgroundColor: [
                    'rgba(78, 115, 223, 0.8)',
                    'rgba(28, 200, 138, 0.8)',
                    'rgba(54, 185, 204, 0.8)',
                    'rgba(246, 194, 62, 0.8)'
                ],
                borderColor: [
                    'rgba(78, 115, 223, 1)',
                    'rgba(28, 200, 138, 1)',
                    'rgba(54, 185, 204, 1)',
                    'rgba(246, 194, 62, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        boxWidth: 12
                    }
                }
            },
            cutout: '70%',
            animation: {
                animateScale: true
            }
        }
    });
});
</script>
<?= $this->endSection() ?>