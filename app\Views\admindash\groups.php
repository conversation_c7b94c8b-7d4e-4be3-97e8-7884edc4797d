<?= $this->extend('templates/adminlte/admindash') ?>
<?= $this->section('content') ?>

<!-- Content Header -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-success">Groups Management</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Groups</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="card card-success card-outline">
            <div class="card-header">
                <h3 class="card-title">Group List</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-success" data-toggle="modal" data-target="#addGroupModal">
                        <i class="fas fa-plus"></i> Add Group
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="groupsTable" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Parent Group</th>
                                <th>Created By</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($groups as $group): ?>
                            <tr>
                                <td><?= esc($group['name']) ?></td>
                                <td><?= esc($group['description']) ?></td>
                                <td>
                                    <?php 
                                        if ($group['parent_id'] == 0) {
                                            echo '<span class="badge badge-success">Main Group</span>';
                                        } else {
                                            $parent = array_filter($groups, function($g) use ($group) {
                                                return $g['id'] == $group['parent_id'];
                                            });
                                            $parent = reset($parent);
                                            echo esc($parent['name'] ?? 'Unknown');
                                        }
                                    ?>
                                </td>
                                <td><?= esc($group['created_by']) ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary" 
                                            onclick="editGroup(<?= esc(json_encode($group)) ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger" 
                                            onclick="confirmDelete(<?= $group['id'] ?>, '<?= esc($group['name']) ?>')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Add Group Modal -->
<div class="modal fade" id="addGroupModal" tabindex="-1" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success">
                <h5 class="modal-title text-white">Add New Group</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?= form_open('groups/add_group') ?>
            <div class="modal-body">
                <div class="form-group">
                    <label>Group Name</label>
                    <input type="text" class="form-control" name="name" required>
                </div>
                <div class="form-group">
                    <label>Description</label>
                    <textarea class="form-control" name="description" rows="3" required></textarea>
                </div>
                <div class="form-group">
                    <label>Parent Group</label>
                    <select class="form-control" name="parent_id">
                        <option value="0">None (Main Group)</option>
                        <?php foreach ($groups as $group): ?>
                            <option value="<?= $group['id'] ?>"><?= esc($group['name']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-success">Add Group</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit Group Modal -->
<div class="modal fade" id="editGroupModal" tabindex="-1" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success">
                <h5 class="modal-title text-white">Edit Group</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?= form_open('groups/update_group') ?>
            <input type="hidden" name="id" id="edit_id">
            <div class="modal-body">
                <div class="form-group">
                    <label>Group Name</label>
                    <input type="text" class="form-control" name="name" id="edit_name" required>
                </div>
                <div class="form-group">
                    <label>Description</label>
                    <textarea class="form-control" name="description" id="edit_description" rows="3" required></textarea>
                </div>
                <div class="form-group">
                    <label>Parent Group</label>
                    <select class="form-control" name="parent_id" id="edit_parent_id">
                        <option value="0">None (Main Group)</option>
                        <?php foreach ($groups as $group): ?>
                            <option value="<?= $group['id'] ?>"><?= esc($group['name']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-success">Update Group</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#groupsTable').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "buttons": ["excel", "pdf", "print"]
    }).buttons().container().appendTo('#groupsTable_wrapper .col-md-6:eq(0)');
});

function editGroup(group) {
    $('#edit_id').val(group.id);
    $('#edit_name').val(group.name);
    $('#edit_description').val(group.description);
    $('#edit_parent_id').val(group.parent_id);
    $('#editGroupModal').modal('show');
}

function confirmDelete(id, name) {
    if (confirm('Are you sure you want to delete group: ' + name + '?\nThis action cannot be undone.')) {
        window.location.href = '<?= base_url() ?>groups/delete_group/' + id;
    }
}
</script>

<style>
.modal-header {
    color: white;
}
.badge {
    font-size: 0.9em;
    padding: 0.5em 1em;
}
</style>

<?= $this->endSection() ?> 