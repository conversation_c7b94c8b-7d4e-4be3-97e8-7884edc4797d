<?= $this->extend('templates/adminlte/admindash') ?>
<?= $this->section('content') ?>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-success">Farm Blocks Report</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Farm Blocks Report</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <!-- Summary Cards -->
        <div class="row">
            <div class="col-12">
                <div class="card card-success card-outline">
                    <div class="card-header">
                        <h3 class="card-title">Summary Statistics</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-3 col-6">
                                <div class="info-box bg-success">
                                    <span class="info-box-icon"><i class="fas fa-map-marker-alt"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Blocks</span>
                                        <span class="info-box-number"><?= $summary['total_blocks'] ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-6">
                                <div class="info-box bg-info">
                                    <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Farmers</span>
                                        <span class="info-box-number"><?= $summary['total_farmers'] ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-6">
                                <div class="info-box bg-warning">
                                    <span class="info-box-icon"><i class="fas fa-seedling"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Crops</span>
                                        <span class="info-box-number"><?= $summary['total_crops'] ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-6">
                                <div class="info-box bg-danger">
                                    <span class="info-box-icon"><i class="fas fa-chart-area"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Hectares</span>
                                        <span class="info-box-number"><?= number_format($summary['total_hectares'], 2) ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Farm Blocks List -->
        <div class="card card-success card-outline">
            <div class="card-header">
                <h3 class="card-title">Farm Blocks List</h3>
            </div>
            <div class="card-body">
                <table id="blocksTable" class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Block Code</th>
                            <th>Farmer</th>
                            <th>Location</th>
                            <th>Crop</th>
                            <th>Plants</th>
                            <th>Hectares</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($farm_blocks as $block): ?>
                            <tr>
                                <td><?= esc($block['block_code']) ?></td>
                                <td>
                                    <?= esc($block['farmer_code']) ?><br>
                                    <small><?= esc($block['given_name']) ?> <?= esc($block['surname']) ?></small>
                                </td>
                                <td>
                                    <small>
                                        <?= esc($block['block_site']) ?><br>
                                        <?= esc($block['ward_name']) ?>, 
                                        <?= esc($block['llg_name']) ?>, 
                                        <?= esc($block['district_name']) ?>
                                    </small>
                                </td>
                                <td>
                                    <i class="<?= $block['crop_icon'] ?>"></i>
                                    <?= esc($block['crop_name']) ?>
                                </td>
                                <td class="text-right">
                                    <?php
                                    $totalPlants = array_sum(array_column($block['crops_data'], 'number_of_plants'));
                                    echo number_format($totalPlants);
                                    ?>
                                </td>
                                <td class="text-right">
                                    <?= number_format($block['total_hectares'], 2) ?>
                                </td>
                                <td class="text-center">
                                    <button type="button" class="btn btn-sm btn-info" 
                                            onclick="viewBlockDetails(<?= esc(json_encode($block)) ?>)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>

<!-- Block Details Modal -->
<div class="modal fade" id="blockDetailsModal">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-success">
                <h5 class="modal-title text-white">Block Details</h5>
                <button type="button" class="close text-white" data-dismiss="modal">×</button>
            </div>
            <div class="modal-body" id="blockDetailsContent">
                <!-- Content will be loaded dynamically -->
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#blocksTable').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "buttons": ["excel", "pdf", "print"]
    }).buttons().container().appendTo('#blocksTable_wrapper .col-md-6:eq(0)');
});

function viewBlockDetails(block) {
    let content = `
        <div class="nav-tabs-custom">
            <ul class="nav nav-tabs">
                <li class="nav-item">
                    <a class="nav-link active" data-toggle="tab" href="#crops">Crops Data</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-toggle="tab" href="#harvest">Harvest Data</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-toggle="tab" href="#fertilizer">Fertilizer Data</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-toggle="tab" href="#pesticides">Pesticides Data</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-toggle="tab" href="#buyers">Crop Buyers</a>
                </li>
            </ul>
            <div class="tab-content">
                <!-- Crops Data -->
                <div class="tab-pane active" id="crops">
                    <!-- Add crops data table here -->
                </div>
                <!-- Harvest Data -->
                <div class="tab-pane" id="harvest">
                    <!-- Add harvest data table here -->
                </div>
                <!-- Fertilizer Data -->
                <div class="tab-pane" id="fertilizer">
                    <!-- Add fertilizer data table here -->
                </div>
                <!-- Pesticides Data -->
                <div class="tab-pane" id="pesticides">
                    <!-- Add pesticides data table here -->
                </div>
                <!-- Crop Buyers -->
                <div class="tab-pane" id="buyers">
                    <!-- Add buyers data table here -->
                </div>
            </div>
        </div>
    `;
    
    $('#blockDetailsContent').html(content);
    $('#blockDetailsModal').modal('show');
}
</script>

<style>
.info-box {
    min-height: 100px;
}
.info-box-icon {
    width: 70px;
    height: 70px;
    line-height: 70px;
}
.table td {
    vertical-align: middle;
}
.nav-tabs-custom {
    margin-bottom: 20px;
    background: #fff;
    box-shadow: 0 1px 1px rgba(0,0,0,0.1);
    border-radius: 3px;
}
.nav-tabs-custom > .nav-tabs {
    margin: 0;
    border-bottom-color: #f4f4f4;
}
</style>

<?= $this->endSection() ?> 